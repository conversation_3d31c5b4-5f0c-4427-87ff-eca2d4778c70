"""
Azure Blob Storage logging configuration module.

This module handles environment variable loading and configuration for Azure Blob Storage logging.
It provides a centralized way to manage Azure logging settings with proper validation and defaults.
"""

import os
from dataclasses import dataclass
from typing import Optional

from dotenv import load_dotenv
from loguru import logger

# Load environment variables
load_dotenv()


@dataclass
class AzureLoggingConfig:
    """
    Configuration class for Azure Blob Storage logging.

    This class loads and validates Azure logging configuration from environment variables,
    providing sensible defaults and proper error handling.
    """

    connection_string: Optional[str] = None
    storage_account: Optional[str] = None
    storage_key: Optional[str] = None
    container_name: str = 'autolodge-logs'
    blob_prefix: str = 'score'
    environment: str = 'UNKNOWN'

    def __post_init__(self):
        """Load configuration from environment variables after initialization."""
        self._load_from_environment()

    def _load_from_environment(self) -> None:
        """Load Azure configuration from environment variables."""
        # Load Azure Blob Storage configuration from environment variables
        self.connection_string = os.getenv('AZURE_STORAGE_CONNECTION_STRING')
        self.storage_account = os.getenv('AZURE_STORAGE_ACCOUNT_NAME')
        self.storage_key = os.getenv('AZURE_STORAGE_ACCOUNT_KEY')
        self.container_name = os.getenv('AZURE_LOG_CONTAINER_NAME', 'autolodge-logs')
        self.blob_prefix = os.getenv('AZURE_LOG_BLOB_PREFIX', 'score')

        # Load environment name with fallback logic
        self.environment = self._detect_environment()

    def _detect_environment(self) -> str:
        """
        Detect the current deployment environment from environment variables.

        This method attempts to determine the environment using multiple strategies:
        1. Direct environment variable (DEPLOYMENT_ENVIRONMENT or APP_ENVIRONMENT)
        2. Azure ML workspace name patterns (extract environment from workspace name)
        3. Model name patterns (extract environment from model name)
        4. Fallback to 'UNKNOWN' if no environment can be determined

        Returns:
            str: Environment name (e.g., 'PROD', 'UAT', 'TEST', 'DEV') or 'UNKNOWN'
        """
        # Strategy 1: Direct environment variable
        direct_env = os.getenv('DEPLOYMENT_ENVIRONMENT') or os.getenv('APP_ENVIRONMENT')
        if direct_env:
            env_upper = direct_env.upper()
            logger.info(f'🔧 Environment detected from direct variable: {env_upper}')
            return env_upper

        # Strategy 2: Extract from Azure ML workspace name
        workspace_name = os.getenv('AZURE_ML_WORKSPACE_NAME')
        if workspace_name:
            env_from_workspace = self._extract_environment_from_name(workspace_name)
            if env_from_workspace:
                logger.info(f'🔧 Environment detected from workspace name: {env_from_workspace}')
                return env_from_workspace

        # Strategy 3: Extract from model name
        model_name = os.getenv('MODEL_NAME')
        if model_name:
            env_from_model = self._extract_environment_from_name(model_name)
            if env_from_model:
                logger.info(f'🔧 Environment detected from model name: {env_from_model}')
                return env_from_model

        # Fallback: Unable to determine environment
        logger.warning('⚠️ Unable to determine deployment environment - using LOCAL')
        logger.info('💡 Set DEPLOYMENT_ENVIRONMENT or APP_ENVIRONMENT variable for explicit environment naming')
        return 'LOCAL'

    def _extract_environment_from_name(self, name: str) -> Optional[str]:
        """
        Extract environment name from Azure resource names.

        Args:
            name: Resource name (workspace, model, etc.)

        Returns:
            Optional[str]: Environment name if found, None otherwise
        """
        name_lower = name.lower()

        # Common environment patterns in Azure resource names
        env_patterns = {
            'prod': ['prod', 'prd', 'production'],
            'UAT': ['uat', 'user-acceptance', 'useracceptance'],
            'PREPROD': ['preprod', 'pre-prod', 'preproduction'],
            'SIT': ['sit', 'system-integration', 'systemintegration'],
            'DEV': ['dev', 'development'],
            'TEST': ['test', 'testing', 'tst', 't-to-tstar'],
        }

        for env_name, patterns in env_patterns.items():
            for pattern in patterns:
                if pattern in name_lower:
                    return env_name.upper()

        return None

    def get_connection_string(self) -> Optional[str]:
        """
        Get the Azure Storage connection string.

        Returns the connection string from environment variables, or constructs one
        from storage account name and key if available.

        Returns:
            Optional[str]: Connection string if available, None otherwise
        """
        if self.connection_string:
            return self.connection_string
        elif self.storage_account and self.storage_key:
            # Construct connection string from account name and key
            return f'DefaultEndpointsProtocol=https;AccountName={self.storage_account};AccountKey={self.storage_key};EndpointSuffix=core.windows.net'
        return None

    def is_configured(self) -> bool:
        """
        Check if Azure Blob Storage logging is properly configured.

        Returns:
            bool: True if Azure configuration is available, False otherwise
        """
        return self.get_connection_string() is not None

    def log_configuration_status(self) -> None:
        """Log the current configuration status with emoji indicators."""
        # Log environment variable loading
        if self.connection_string:
            logger.info('🔧 Loaded Azure Storage connection string from environment variables')
        elif self.storage_account and self.storage_key:
            logger.info('🔧 Loaded Azure Storage account credentials from environment variables')

        logger.info(f'🔧 Azure Blob Storage container: {self.container_name}')
        logger.info(f'🔧 Azure Blob Storage prefix: {self.blob_prefix}')
        logger.info(f'🔧 Deployment environment: {self.environment}')

    def log_configuration_hints(self) -> None:
        """Log helpful configuration hints when Azure is not configured."""
        if not self.is_configured():
            logger.warning('⚠️ Azure Storage connection string not configured')
            logger.info(
                '💡 Set AZURE_STORAGE_CONNECTION_STRING or (AZURE_STORAGE_ACCOUNT_NAME + AZURE_STORAGE_ACCOUNT_KEY) environment variables to enable Azure Blob Storage logging'
            )


def get_azure_logging_config() -> AzureLoggingConfig:
    """
    Get Azure logging configuration from environment variables.

    Returns:
        AzureLoggingConfig: Configuration object with Azure settings
    """
    return AzureLoggingConfig()


def check_azure_dependencies() -> bool:
    """
    Check if Azure Blob Storage dependencies are available.

    Returns:
        bool: True if Azure dependencies are available, False otherwise
    """
    try:
        import threading
        import time

        from azure.storage.blob import BlobServiceClient

        return True
    except ImportError:
        return False
