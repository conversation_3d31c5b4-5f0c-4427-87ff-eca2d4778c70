"""
Pytest configuration and fixtures for score.py tests.
"""

import os
import sys
import tempfile
from pathlib import Path
from unittest.mock import Mock

import pytest

# Add the Python directory to the path so we can import score
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'Python'))


@pytest.fixture
def temp_dir():
    """Create a temporary directory for testing."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def mock_model():
    """Create a mock Keras model."""
    model = Mock()
    model.predict.return_value = [[0.1, 0.2, 0.7], [0.6, 0.3, 0.1]]
    return model


@pytest.fixture
def mock_tokenizer():
    """Create a mock tokenizer."""
    tokenizer = Mock()
    tokenizer.texts_to_matrix.return_value = [[1, 0, 1], [0, 1, 0]]
    return tokenizer


@pytest.fixture
def mock_label_encoder():
    """Create a mock label encoder."""
    encoder = Mock()
    encoder.inverse_transform.return_value = ['label1', 'label2']
    return encoder


@pytest.fixture
def mock_spell_checker():
    """Create a mock SymSpell spell checker."""
    spell_checker = Mock()
    suggestion = Mock()
    suggestion.distance = 1
    suggestion.term = 'corrected'
    spell_checker.lookup.return_value = [suggestion]
    return spell_checker


@pytest.fixture
def sample_abbreviation_data():
    """Sample abbreviation data for testing."""
    return {
        'Abbreviation': ['ABC', 'DEF', 'CS_ABBR'],
        'FullText': ['full_abc', 'full_def', 'case_sensitive_full'],
        'Comment': [None, None, 'CS'],
    }


@pytest.fixture
def sample_input_data():
    """Sample input data for testing."""
    return [
        {'Pair_ID': 1001, 'T': 'Discount to Invoice', 'AmountExVat': 80},
        {'Pair_ID': 1002, 'T': 'Service Fee $9.9900 S', 'AmountExVat': 9.99},
        {'Pair_ID': 1003, 'T': '', 'AmountExVat': 0},  # Empty treatment for T202 rule testing
    ]


@pytest.fixture(autouse=True)
def reset_global_state():
    """Reset global state before each test."""
    # Import score module to reset its global state
    import sys
    from pathlib import Path

    sys.path.insert(0, str(Path(__file__).parent.parent))
    from autolodge import score

    score._resource_manager = None
    yield
    # Clean up after test
    score._resource_manager = None
